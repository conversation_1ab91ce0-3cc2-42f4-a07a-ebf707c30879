<template>
  <div class="min-h-screen bg-gradient-to-b from-slate-900 via-slate-800 to-slate-900 text-white">
    <div class="max-w-7xl mx-auto p-6">
      <div class="flex items-center justify-between mb-6">
        <h1 class="text-3xl font-semibold">livetalking数字人交互平台</h1>
        <div class="flex items-center gap-2 text-sm">
          <span class="inline-block w-2.5 h-2.5 rounded-full" :class="{
            'bg-emerald-400': connectionStatus==='connected',
            'bg-amber-400': connectionStatus==='connecting',
            'bg-rose-400': connectionStatus==='disconnected'
          }"/>
          <span class="opacity-80">{{ statusText }}</span>
        </div>
      </div>

      <!-- 上方：数字人视频（居中） -->
      <div class="max-w-4xl mx-auto relative z-0">
        <div class="rounded-2xl overflow-hidden bg-gradient-to-b from-slate-900 to-black border border-white/10 shadow-[0_10px_40px_-10px_rgba(0,0,0,0.6)]">
          <div class="bg-black flex items-center justify-center select-none" :style="{ height: videoHeight }">
            <video ref="videoEl" autoplay playsinline class="w-full h-full object-contain pointer-events-none"></video>
          </div>
        </div>
      </div>

      <!-- 底部：悬浮工具栏（科技感玻璃态） - 吸底并高于视频层 -->
      <div class="px-4 mt-4 sticky bottom-0 z-20">
        <div class="max-w-4xl mx-auto rounded-2xl border border-white/10 bg-white/5 backdrop-blur-xl shadow-[0_8px_30px_rgba(0,0,0,0.4)] pointer-events-auto">
          <div class="px-4 py-3 flex items-center justify-between gap-3 text-sm">
            <!-- 左侧：连接/录制 -->
            <div class="flex items-center gap-2">
              <button v-if="!connected" @click="onStart" class="px-3 py-2 rounded-lg bg-primary hover:bg-indigo-600">
                <i class="bi bi-play-fill"></i> 开始连接
              </button>
              <button v-else @click="onStop" class="px-3 py-2 rounded-lg bg-rose-600 hover:bg-rose-500">
                <i class="bi bi-stop-fill"></i> 停止连接
              </button>

              <button @click="startRecord" :disabled="!connected || recording" class="px-3 py-2 rounded-lg border border-white/10 hover:bg-white/10 disabled:opacity-50">
                <i class="bi bi-record-fill"></i> 开始录制
              </button>
              <button @click="stopRecord" :disabled="!connected || !recording" class="px-3 py-2 rounded-lg border border-white/10 hover:bg-white/10 disabled:opacity-50">
                <i class="bi bi-stop-fill"></i> 停止录制
              </button>
            </div>

            <!-- 中间：按住说话 -->
            <div class="flex-1 flex items-center justify-center">
              <div
                @mousedown.prevent="pressToTalk(true)"
                @mouseup.prevent="pressToTalk(false)"
                @mouseleave.prevent="pressToTalk(false)"
                @touchstart.prevent="pressToTalk(true)"
                @touchend.prevent="pressToTalk(false)"
                class="w-14 h-14 rounded-full flex items-center justify-center cursor-pointer transition-all shadow-md ring-1 ring-white/20"
                :class="recording ? 'bg-rose-600 animate-pulse' : 'bg-primary hover:bg-indigo-600'">
                <i class="bi bi-mic-fill text-xl"></i>
              </div>
            </div>

            <!-- 右侧：功能入口 -->
            <div class="flex items-center gap-2">
              <button @click="showChat = true" class="px-3 py-2 rounded-lg border border-white/10 hover:bg-white/10">
                <i class="bi bi-chat-dots"></i> 交互
              </button>
              <button @click="showSettings = true" class="px-3 py-2 rounded-lg border border-white/10 hover:bg-white/10">
                <i class="bi bi-sliders"></i> 设置
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧抽屉：交互（对话 / 朗读） -->
      <div v-if="showChat" class="fixed inset-0 z-50 flex">
        <div class="flex-1" @click="showChat=false"></div>
        <div class="w-full max-w-md h-full bg-slate-900/90 backdrop-blur-xl border-l border-white/10 shadow-2xl">
          <div class="p-4 border-b border-white/10 flex items-center justify-between">
            <div class="font-medium">互动面板</div>
            <button @click="showChat=false" class="p-2 rounded hover:bg-white/10"><i class="bi bi-x-lg"></i></button>
          </div>
          <div class="p-4">
            <div class="mb-3 flex gap-3">
              <button :class="tab==='chat' ? activeTabCls : tabCls" @click="tab='chat'">对话模式</button>
              <button :class="tab==='tts' ? activeTabCls : tabCls" @click="tab='tts'">朗读模式</button>
            </div>

            <!-- 对话模式 -->
            <div v-show="tab==='chat'" class="space-y-4">
              <div ref="chatBox" class="h-80 overflow-y-auto space-y-3 p-3 rounded-lg bg-slate-900/40 border border-white/10">
                <div class="p-2 rounded border-l-4 border-emerald-500 bg-emerald-500/10">系统: 欢迎使用livetalking，请点击"开始连接"按钮开始对话。</div>
                <div v-for="(m,i) in messages" :key="i" :class="m.role==='user' ? 'bg-sky-500/10 border-sky-500' : 'bg-emerald-500/10 border-emerald-500'" class="p-2 rounded border-l-4">
                  {{ m.role==='user' ? '您' : '数字人' }}: {{ m.text }}
                </div>
              </div>
              <div class="space-y-3">
                <div class="flex gap-2">
                  <textarea v-model="chatInput" rows="3" placeholder="输入您想对数字人说的话..." class="flex-1 p-2 rounded bg-slate-900/40 border border-white/10"></textarea>
                  <button @click="sendChat" class="px-4 py-2 rounded-lg bg-primary hover:bg-indigo-600">发送</button>
                </div>
                <div class="text-center text-xs opacity-70">按住底部麦克风可语音输入</div>
              </div>
            </div>

            <!-- 朗读模式 -->
            <div v-show="tab==='tts'" class="space-y-3">
              <label class="text-sm opacity-80">输入要朗读的文本</label>
              <textarea v-model="ttsInput" rows="8" placeholder="输入您想让数字人朗读的文字..." class="w-full p-2 rounded bg-slate-900/40 border border-white/10"></textarea>
              <button @click="sendTTS" class="w-full px-4 py-2 rounded-lg bg-primary hover:bg-indigo-600"><i class="bi bi-volume-up"></i> 朗读文本</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧抽屉：设置 -->
      <div v-if="showSettings" class="fixed inset-0 z-50 flex">
        <div class="flex-1" @click="showSettings=false"></div>
        <div class="w-full max-w-md h-full bg-slate-900/90 backdrop-blur-xl border-l border-white/10 shadow-2xl">
          <div class="p-4 border-b border-white/10 flex items-center justify-between">
            <div class="font-medium">设置</div>
            <button @click="showSettings=false" class="p-2 rounded hover:bg-white/10"><i class="bi bi-x-lg"></i></button>
          </div>
          <div class="p-4 space-y-6">
            <div>
              <label class="text-sm opacity-80">视频大小调节: <span>{{ videoScale }}%</span></label>
              <input type="range" min="50" max="150" v-model.number="videoScale" class="w-full" />
            </div>
            <label class="inline-flex items-center gap-2 cursor-pointer">
              <input type="checkbox" v-model="useStun" class="accent-indigo-500" />
              <span>使用STUN服务器</span>
            </label>
          </div>
        </div>
      </div>

      <div class="text-center text-slate-400 text-sm mt-8">Made with ❤️ by Marstaos | Frontend & Performance Optimization</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Dashboard',
  data() {
    return {
      tab: 'chat',
      connected: false,
      recording: false,
      connectionStatus: 'disconnected',
      chatInput: '',
      ttsInput: '',
      messages: [],
      sessionId: 0,
      useStun: false,
      videoScale: 100,
      pc: null,
      recognition: null,
      showChat: false,
      showSettings: false
    }
  },
  computed: {
    statusText() {
      const map = { disconnected: '未连接', connecting: '连接中...', connected: '已连接' }
      return map[this.connectionStatus] || '未连接'
    },
    videoHeight() {
      // 以 480px 为基准高度，按比例缩放
      const base = 480
      return `${Math.round(base * (this.videoScale / 100))}px`
    },
    tabCls() {
      return 'px-3 py-2 rounded-lg border border-white/10 hover:bg-white/10 transition-colors'
    },
    activeTabCls() {
      return 'px-3 py-2 rounded-lg bg-primary text-white shadow'
    }
  },
  mounted() {
    // 语音识别能力探测
    const SR = window.SpeechRecognition || window.webkitSpeechRecognition
    if (SR) {
      this.recognition = new SR()
      this.recognition.continuous = true
      this.recognition.interimResults = true
      this.recognition.lang = 'zh-CN'
      this.recognition.onresult = (event) => {
        let interim = ''
        let finalTxt = ''
        for (let i = event.resultIndex; i < event.results.length; ++i) {
          if (event.results[i].isFinal) finalTxt += event.results[i][0].transcript
          else interim += event.results[i][0].transcript
        }
        if (interim) this.chatInput = interim
        if (finalTxt) this.chatInput = finalTxt
      }
      this.recognition.onerror = e => console.error('语音识别错误:', e.error)
    }
  },
  methods: {
    async onStart() {
      try {
        this.connectionStatus = 'connecting'
        const config = { sdpSemantics: 'unified-plan' }
        if (this.useStun) config.iceServers = [{ urls: ['stun:stun.l.google.com:19302'] }]

        this.pc = new RTCPeerConnection(config)
        this.pc.addEventListener('track', (evt) => {
          if (evt.track.kind === 'video') this.$refs.videoEl.srcObject = evt.streams[0]
        })
        this.pc.addTransceiver('video', { direction: 'recvonly' })
        this.pc.addTransceiver('audio', { direction: 'recvonly' })

        const offer = await this.pc.createOffer()
        await this.pc.setLocalDescription(offer)
        await new Promise((resolve) => {
          if (this.pc.iceGatheringState === 'complete') return resolve()
          const check = () => {
            if (this.pc.iceGatheringState === 'complete') {
              this.pc.removeEventListener('icegatheringstatechange', check)
              resolve()
            }
          }
          this.pc.addEventListener('icegatheringstatechange', check)
        })

        const resp = await fetch('/offer', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ sdp: this.pc.localDescription.sdp, type: this.pc.localDescription.type })
        })
        if (!resp.ok) throw new Error('服务器响应错误')
        const answer = await resp.json()
        this.sessionId = answer.sessionid
        await this.pc.setRemoteDescription(answer)

        this.connected = true
        this.connectionStatus = 'connected'
      } catch (e) {
        console.error(e)
        this.connectionStatus = 'disconnected'
        this.connected = false
      }
    },
    onStop() {
      if (this.pc) {
        setTimeout(() => this.pc.close(), 500)
      }
      this.connected = false
      this.connectionStatus = 'disconnected'
    },
    async startRecord() {
      try {
        const r = await fetch('/record', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ type: 'start_record', sessionid: this.sessionId })
        })
        if (r.ok) this.recording = true
      } catch (e) { console.error(e) }
    },
    async stopRecord() {
      try {
        const r = await fetch('/record', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ type: 'end_record', sessionid: this.sessionId })
        })
        if (r.ok) this.recording = false
      } catch (e) { console.error(e) }
    },
    async sendChat() {
      const text = (this.chatInput || '').trim()
      if (!text) return
      this.messages.push({ role: 'user', text })
      this.$nextTick(() => { const el = this.$refs.chatBox; if (el) el.scrollTop = el.scrollHeight })

      try {
        await fetch('/human', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ text, type: 'chat', interrupt: true, sessionid: this.sessionId })
        })
      } catch (e) { console.error(e) }
      this.chatInput = ''
    },
    async sendTTS() {
      const text = (this.ttsInput || '').trim()
      if (!text) return
      try {
        await fetch('/human', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ text, type: 'echo', interrupt: true, sessionid: this.sessionId })
        })
        this.messages.push({ role: 'system', text: `已发送朗读请求: "${text}"` })
        this.$nextTick(() => { const el = this.$refs.chatBox; if (el) el.scrollTop = el.scrollHeight })
      } catch (e) { console.error(e) }
      this.ttsInput = ''
    },
    pressToTalk(start) {
      if (!this.recognition) return
      if (start && !this.recording) {
        this.recording = true
        try { this.recognition.start() } catch {}
      } else if (!start && this.recording) {
        this.recording = false
        try { this.recognition.stop() } catch {}
        setTimeout(() => { // 取识别文本发 chat
          const recognizedText = (this.chatInput || '').trim()
          if (recognizedText) {
            this.messages.push({ role: 'user', text: recognizedText })
            fetch('/human', {
              method: 'POST', headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ text: recognizedText, type: 'chat', interrupt: true, sessionid: this.sessionId })
            })
            this.chatInput = ''
          }
        }, 300)
      }
    }
  }
}
</script>

<style scoped>
/* 主色（与原始配色兼容） */
.bg-primary { background-color: var(--primary, #4361ee); }
</style>

