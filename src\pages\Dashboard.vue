<template>
  <div class="dashboard-container">
    <div class="dashboard-content">
      <div class="dashboard-header">
        <h1 class="dashboard-title">livetalking数字人交互平台</h1>
        <div class="status-indicator">
          <span class="status-dot" :class="{
            'connected': connectionStatus==='connected',
            'connecting': connectionStatus==='connecting',
            'disconnected': connectionStatus==='disconnected'
          }"></span>
          <span class="status-text">{{ statusText }}</span>
        </div>
      </div>

      <!-- 数字人视频容器 - 居中显示 -->
      <div class="video-container">
        <div class="video-wrapper">
          <div class="video-frame">
            <div class="video-content" :style="{ height: videoHeight }">
              <video ref="videoEl" autoplay playsinline class="video-element"></video>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部工具栏 - 固定在底部，不覆盖视频 -->
      <div class="toolbar-container">
        <div class="toolbar">
          <div class="toolbar-content">
            <!-- 左侧：连接/录制 -->
            <div class="toolbar-left">
              <button v-if="!connected" @click="onStart" class="btn btn-primary">
                <i class="bi bi-play-fill"></i> 开始连接
              </button>
              <button v-else @click="onStop" class="btn btn-danger">
                <i class="bi bi-stop-fill"></i> 停止连接
              </button>

              <button @click="startRecord" :disabled="!connected || recording" class="btn btn-secondary">
                <i class="bi bi-record-fill"></i> 开始录制
              </button>
              <button @click="stopRecord" :disabled="!connected || !recording" class="btn btn-secondary">
                <i class="bi bi-stop-fill"></i> 停止录制
              </button>
            </div>

            <!-- 中间：按住说话 -->
            <div class="toolbar-center">
              <div
                @mousedown.prevent="pressToTalk(true)"
                @mouseup.prevent="pressToTalk(false)"
                @mouseleave.prevent="pressToTalk(false)"
                @touchstart.prevent="pressToTalk(true)"
                @touchend.prevent="pressToTalk(false)"
                class="mic-button"
                :class="recording ? 'recording' : 'idle'">
                <i class="bi bi-mic-fill"></i>
              </div>
            </div>

            <!-- 右侧：功能入口 -->
            <div class="toolbar-right">
              <button @click="showChat = true" class="btn btn-secondary">
                <i class="bi bi-chat-dots"></i> 交互
              </button>
              <button @click="showSettings = true" class="btn btn-secondary">
                <i class="bi bi-sliders"></i> 设置
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧抽屉：交互（对话 / 朗读） -->
      <div v-if="showChat" class="sidebar-overlay">
        <div class="sidebar-backdrop" @click="showChat=false"></div>
        <div class="sidebar">
          <div class="sidebar-header">
            <div class="sidebar-title">互动面板</div>
            <button @click="showChat=false" class="btn btn-secondary"><i class="bi bi-x-lg"></i></button>
          </div>
          <div class="sidebar-content">
            <div class="tab-container">
              <button :class="tab==='chat' ? 'tab-button active' : 'tab-button'" @click="tab='chat'">对话模式</button>
              <button :class="tab==='tts' ? 'tab-button active' : 'tab-button'" @click="tab='tts'">朗读模式</button>
            </div>

            <!-- 对话模式 -->
            <div v-show="tab==='chat'" class="chat-container">
              <div ref="chatBox" class="chat-box">
                <div class="chat-message system">系统: 欢迎使用livetalking，请点击"开始连接"按钮开始对话。</div>
                <div v-for="(m,i) in messages" :key="i" :class="'chat-message ' + (m.role==='user' ? 'user' : 'assistant')">
                  {{ m.role==='user' ? '您' : '数字人' }}: {{ m.text }}
                </div>
              </div>
              <div class="chat-input-container">
                <textarea v-model="chatInput" rows="3" placeholder="输入您想对数字人说的话..." class="chat-textarea"></textarea>
                <button @click="sendChat" class="btn btn-primary">发送</button>
              </div>
              <div class="chat-hint">按住底部麦克风可语音输入</div>
            </div>

            <!-- 朗读模式 -->
            <div v-show="tab==='tts'" class="tts-container">
              <label class="tts-label">输入要朗读的文本</label>
              <textarea v-model="ttsInput" rows="8" placeholder="输入您想让数字人朗读的文字..." class="tts-textarea"></textarea>
              <button @click="sendTTS" class="btn btn-primary" style="width: 100%;"><i class="bi bi-volume-up"></i> 朗读文本</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧抽屉：设置 -->
      <div v-if="showSettings" class="sidebar-overlay">
        <div class="sidebar-backdrop" @click="showSettings=false"></div>
        <div class="sidebar">
          <div class="sidebar-header">
            <div class="sidebar-title">设置</div>
            <button @click="showSettings=false" class="btn btn-secondary"><i class="bi bi-x-lg"></i></button>
          </div>
          <div class="sidebar-content settings-content">
            <div class="setting-item">
              <label class="setting-label">视频大小调节: <span>{{ videoScale }}%</span></label>
              <input type="range" min="50" max="150" v-model.number="videoScale" class="range-input" />
            </div>
            <label class="checkbox-label">
              <input type="checkbox" v-model="useStun" class="checkbox-input" />
              <span>使用STUN服务器</span>
            </label>
          </div>
        </div>
      </div>

      <!-- 版权信息 -->
      <div class="footer">Made with ❤️ by Marstaos | Frontend & Performance Optimization</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Dashboard',
  data() {
    return {
      tab: 'chat',
      connected: false,
      recording: false,
      connectionStatus: 'disconnected',
      chatInput: '',
      ttsInput: '',
      messages: [],
      sessionId: 0,
      useStun: false,
      videoScale: 100,
      pc: null,
      recognition: null,
      showChat: false,
      showSettings: false
    }
  },
  computed: {
    statusText() {
      const map = { disconnected: '未连接', connecting: '连接中...', connected: '已连接' }
      return map[this.connectionStatus] || '未连接'
    },
    videoHeight() {
      // 以 480px 为基准高度，按比例缩放
      const base = 480
      return `${Math.round(base * (this.videoScale / 100))}px`
    },

  },
  mounted() {
    // 语音识别能力探测
    const SR = window.SpeechRecognition || window.webkitSpeechRecognition
    if (SR) {
      this.recognition = new SR()
      this.recognition.continuous = true
      this.recognition.interimResults = true
      this.recognition.lang = 'zh-CN'
      this.recognition.onresult = (event) => {
        let interim = ''
        let finalTxt = ''
        for (let i = event.resultIndex; i < event.results.length; ++i) {
          if (event.results[i].isFinal) finalTxt += event.results[i][0].transcript
          else interim += event.results[i][0].transcript
        }
        if (interim) this.chatInput = interim
        if (finalTxt) this.chatInput = finalTxt
      }
      this.recognition.onerror = e => console.error('语音识别错误:', e.error)
    }
  },
  methods: {
    async onStart() {
      try {
        this.connectionStatus = 'connecting'
        const config = { sdpSemantics: 'unified-plan' }
        if (this.useStun) config.iceServers = [{ urls: ['stun:stun.l.google.com:19302'] }]

        this.pc = new RTCPeerConnection(config)
        this.pc.addEventListener('track', (evt) => {
          if (evt.track.kind === 'video') this.$refs.videoEl.srcObject = evt.streams[0]
        })
        this.pc.addTransceiver('video', { direction: 'recvonly' })
        this.pc.addTransceiver('audio', { direction: 'recvonly' })

        const offer = await this.pc.createOffer()
        await this.pc.setLocalDescription(offer)
        await new Promise((resolve) => {
          if (this.pc.iceGatheringState === 'complete') return resolve()
          const check = () => {
            if (this.pc.iceGatheringState === 'complete') {
              this.pc.removeEventListener('icegatheringstatechange', check)
              resolve()
            }
          }
          this.pc.addEventListener('icegatheringstatechange', check)
        })

        const resp = await fetch('/offer', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ sdp: this.pc.localDescription.sdp, type: this.pc.localDescription.type })
        })
        if (!resp.ok) throw new Error('服务器响应错误')
        const answer = await resp.json()
        this.sessionId = answer.sessionid
        await this.pc.setRemoteDescription(answer)

        this.connected = true
        this.connectionStatus = 'connected'
      } catch (e) {
        console.error(e)
        this.connectionStatus = 'disconnected'
        this.connected = false
      }
    },
    onStop() {
      if (this.pc) {
        setTimeout(() => this.pc.close(), 500)
      }
      this.connected = false
      this.connectionStatus = 'disconnected'
    },
    async startRecord() {
      try {
        const r = await fetch('/record', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ type: 'start_record', sessionid: this.sessionId })
        })
        if (r.ok) this.recording = true
      } catch (e) { console.error(e) }
    },
    async stopRecord() {
      try {
        const r = await fetch('/record', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ type: 'end_record', sessionid: this.sessionId })
        })
        if (r.ok) this.recording = false
      } catch (e) { console.error(e) }
    },
    async sendChat() {
      const text = (this.chatInput || '').trim()
      if (!text) return
      this.messages.push({ role: 'user', text })
      this.$nextTick(() => { const el = this.$refs.chatBox; if (el) el.scrollTop = el.scrollHeight })

      try {
        await fetch('/human', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ text, type: 'chat', interrupt: true, sessionid: this.sessionId })
        })
      } catch (e) { console.error(e) }
      this.chatInput = ''
    },
    async sendTTS() {
      const text = (this.ttsInput || '').trim()
      if (!text) return
      try {
        await fetch('/human', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ text, type: 'echo', interrupt: true, sessionid: this.sessionId })
        })
        this.messages.push({ role: 'system', text: `已发送朗读请求: "${text}"` })
        this.$nextTick(() => { const el = this.$refs.chatBox; if (el) el.scrollTop = el.scrollHeight })
      } catch (e) { console.error(e) }
      this.ttsInput = ''
    },
    pressToTalk(start) {
      if (!this.recognition) return
      if (start && !this.recording) {
        this.recording = true
        try { this.recognition.start() } catch {}
      } else if (!start && this.recording) {
        this.recording = false
        try { this.recognition.stop() } catch {}
        setTimeout(() => { // 取识别文本发 chat
          const recognizedText = (this.chatInput || '').trim()
          if (recognizedText) {
            this.messages.push({ role: 'user', text: recognizedText })
            fetch('/human', {
              method: 'POST', headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ text: recognizedText, type: 'chat', interrupt: true, sessionid: this.sessionId })
            })
            this.chatInput = ''
          }
        }, 300)
      }
    }
  }
}
</script>

<style scoped>
/* 主色（与原始配色兼容） */
.bg-primary { background-color: var(--primary, #4361ee); }
</style>

