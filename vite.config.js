import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  server: {
    port: 5173,
    proxy: {
      // 后端 aiohttp 默认 8010
      '^/(offer|human|humanaudio|set_audiotype|record|interrupt_talk|is_speaking)$': {
        target: 'http://localhost:8010',
        changeOrigin: true,
      },
      // 可选：SRS 1985 WHEP/WHIP
      '^/rtc/.*': {
        target: 'http://localhost:1985',
        changeOrigin: true,
      }
    }
  }
})
