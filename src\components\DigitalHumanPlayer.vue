<template>
  <div class="relative rounded-lg overflow-hidden bg-black" :style="{height}">
    <video ref="videoEl" autoplay playsinline class="w-full h-full object-contain"></video>
    <div class="absolute top-3 right-3 text-xs px-2 py-1 rounded-full backdrop-blur bg-black/50" :class="{
      'text-emerald-300': status==='connected',
      'text-amber-300': status==='connecting',
      'text-rose-300': status==='disconnected'
    }">
      {{ statusText }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'DigitalHumanPlayer',
  props: {
    height: { type: String, default: '480px' },
    useStun: { type: Boolean, default: false }
  },
  data() {
    return {
      status: 'disconnected',
      pc: null,
      sessionId: 0
    }
  },
  computed: {
    statusText() {
      const m = { disconnected: '未连接', connecting: '连接中...', connected: '已连接' }
      return m[this.status] || '未连接'
    }
  },
  methods: {
    async connect() {
      try {
        this.status = 'connecting'
        const config = { sdpSemantics: 'unified-plan' }
        if (this.useStun) config.iceServers = [{ urls: ['stun:stun.l.google.com:19302'] }]
        this.pc = new RTCPeerConnection(config)
        this.pc.addEventListener('track', (evt) => {
          if (evt.track.kind === 'video') this.$refs.videoEl.srcObject = evt.streams[0]
        })
        this.pc.addTransceiver('video', { direction: 'recvonly' })
        this.pc.addTransceiver('audio', { direction: 'recvonly' })
        const offer = await this.pc.createOffer()
        await this.pc.setLocalDescription(offer)
        await new Promise((resolve) => {
          if (this.pc.iceGatheringState === 'complete') return resolve()
          const check = () => {
            if (this.pc.iceGatheringState === 'complete') { this.pc.removeEventListener('icegatheringstatechange', check); resolve() }
          }
          this.pc.addEventListener('icegatheringstatechange', check)
        })
        const resp = await fetch('/offer', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ sdp: this.pc.localDescription.sdp, type: this.pc.localDescription.type }) })
        if (!resp.ok) throw new Error('服务器响应错误')
        const answer = await resp.json()
        this.sessionId = answer.sessionid
        await this.pc.setRemoteDescription(answer)
        this.status = 'connected'
        this.$emit('connected', { sessionId: this.sessionId })
      } catch (e) {
        console.error(e)
        this.status = 'disconnected'
      }
    },
    disconnect() {
      if (this.pc) setTimeout(() => this.pc.close(), 500)
      this.status = 'disconnected'
    }
  }
}
</script>

<style scoped>
</style>

